# frozen_string_literal: true

class User < AtharAuth::Models::User
  # Inherit all common attributes from AtharAuth::Models::User:
  # - id, name, email, global, project_id, user_type, scope, permissions
  # - global_user?, project_based_user?, can?(permission)
  # - from_token_data factory method with rich associations

  # Procure-specific ActiveStruct associations
  has_many :procurement_requests, class_name: "ProcurementRequest", foreign_key: :requester_id, primary_key: :id
  has_many :approved_requests, class_name: "ProcurementRequest", foreign_key: :approver_id, primary_key: :id

  # Procure-specific domain methods
  def accessible_requests
    if global_user?
      ProcurementRequest.all
    elsif procurement_manager?
      ProcurementRequest.where(project_id: project_id)
    else
      procurement_requests.where(project_id: project_id)
    end
  end

  def can_approve_request?(request)
    return true if global_user?
    return true if procurement_manager? && request.project_id == project_id
    false
  end

  def accessible_suppliers
    if global_user?
      Supplier.all
    elsif procurement_manager?
      Supplier.for_project(project_id)
    else
      Supplier.none
    end
  end

  def can_manage_supplier?(supplier)
    return true if global_user?
    return true if procurement_manager? && supplier_in_accessible_projects?(supplier)
    false
  end

  def accessible_purchase_orders
    if global_user?
      PurchaseOrder.all
    elsif procurement_manager?
      PurchaseOrder.where(project_id: project_id)
    else
      PurchaseOrder.where(requester_id: id, project_id: project_id)
    end
  end

  def can_manage_purchase_order?(order)
    return true if global_user?
    return true if procurement_manager? && order.project_id == project_id
    return true if order.requester_id == id
    false
  end

  # Procure-specific role checks
  def procurement_manager?
    role&.name&.include?("procurement_manager")
  end

  def procurement_officer?
    role&.name&.include?("procurement_officer")
  end

  def finance_manager?
    role&.name == "financial_manager"
  end

  def accountant?
    role&.name == "accountant"
  end

  # Use inherited factory method from AtharAuth::Models::User
  # No need to override - from_token_data handles everything with rich associations

  private

  def supplier_in_accessible_projects?(supplier)
    return true if global_user?
    return false unless project_id

    # Check if supplier is associated with user's project
    supplier.project_id == project_id
  end
end
