class ProcurementRequest < ApplicationRecord
  belongs_to :item
  belongs_to :requester, class_name: "User", foreign_key: :requester_id

  validates :status, presence: true, inclusion: { in: %w[pending approved rejected canceled] }
  validates :quantity, numericality: { greater_than: 0 }

  scope :pending, -> { where(status: "pending") }
  scope :approved, -> { where(status: "approved") }
  scope :by_user, ->(user_id) { where(requester_id: user_id) }
end
