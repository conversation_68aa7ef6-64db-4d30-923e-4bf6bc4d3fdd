class Item < ApplicationRecord
  # Associations
  belongs_to :creator, class_name: "User", foreign_key: :created_by_id, optional: true

  # Validations
  validates :name, presence: true
  validates :category, presence: true
  validates :approx_price, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :created_by_id, presence: true

  has_many :procurement_request_items
  has_many :procurement_requests, through: :procurement_request_items

  # Scopes
  scope :by_category, ->(cat) { where(category: cat) if cat.present? }

  # Optional: categorization enum (if categories are fixed)
  # enum category: { electronics: "Electronics", appliances: "Appliances", software: "Software" }
end