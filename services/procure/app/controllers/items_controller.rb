class ItemsController < ApplicationController
  before_action :authenticate_session!
  before_action :set_item, only: %i[show update destroy]

  api :GET, "/items", "Lists all predefined procurement items"
  header "Authorization", "Scoped session token as Bearer token", required: true
  description <<-HTML
    Returns a list of predefined items that can be requested by employees.<br>
    Only Project Managers are allowed to manage these items.<br>
    Requires permission: <code>:read, :item</code>.
  HTML
  returns code: 200, desc: "List of items"

  def index
    return unless authorize!(:read, :item)

    @items = Item.all
    render json: @items
  end

  api :GET, "/items/:id", "Retrieves a specific item"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the item"
  description <<-HTML
    Fetches detailed information about a predefined procurement item.<br>
    Requires permission: <code>:read, :item</code>.
  HTML
  returns code: 200, desc: "Item details"

  def show
    return unless authorize!(:read, :item)

    render json: @item
  end

  api :POST, "/items", "Creates a new procurement item"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :item, Hash, required: true, desc: "Item attributes" do
    param :name, String, required: true, desc: "Item name"
    param :description, String, required: false, desc: "Item description"
    param :category, String, required: true, desc: "Category of the item (e.g., electronics)"
    param :approx_price, Float, required: true, desc: "Estimated price"
    param :vendor_name, String, required: false, desc: "Vendor or supplier name"
    param :created_by_id, String, required: true, desc: "User ID who created the item"
  end
  description <<-HTML
    Creates a new predefined item in the system.<br>
    Only Project Managers are authorized to create items.<br>
    Requires permission: <code>:create, :item</code>.
  HTML
  returns code: 201, desc: "Item created successfully"
  error code: 422, desc: "Validation errors"

  def create
    return unless authorize!(:create, :item)

    @item = Item.new(item_params)
    if @item.save
      render json: @item, status: :created, location: @item
    else
      render json: @item.errors, status: :unprocessable_entity
    end
  end

  api :PUT, "/items/:id", "Updates a procurement item"
  api :PATCH, "/items/:id", "Partially updates a procurement item"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the item"
  param :item, Hash, required: true, desc: "Item fields to update" do
    param :name, String, desc: "Item name"
    param :description, String, desc: "Item description"
    param :category, String, desc: "Category"
    param :approx_price, Float, desc: "Estimated price"
    param :vendor_name, String, desc: "Vendor name"
    param :created_by_id, String, desc: "User ID who created the item"
  end
  description <<-HTML
    Updates an existing item.<br>
    Only Project Managers can update items.<br>
    Requires permission: <code>:update, :item</code>.
  HTML
  returns code: 200, desc: "Item updated successfully"
  error code: 422, desc: "Validation errors"

  def update
    return unless authorize!(:update, :item)

    if @item.update(item_params)
      render json: @item
    else
      render json: @item.errors, status: :unprocessable_entity
    end
  end

  api :DELETE, "/items/:id", "Deletes a procurement item"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the item"
  description <<-HTML
    Deletes a predefined procurement item.<br>
    Only Project Managers can delete items.<br>
    Requires permission: <code>:destroy, :item</code>.
  HTML
  returns code: 204, desc: "Item deleted successfully"

  def destroy
    return unless authorize!(:destroy, :item)

    @item.destroy!
  end

  private

  def set_item
    @item = Item.find(params[:id])
  end

  def item_params
    params.require(:item).permit(:name, :description, :category, :approx_price, :vendor_name, :created_by_id)
  end
end
