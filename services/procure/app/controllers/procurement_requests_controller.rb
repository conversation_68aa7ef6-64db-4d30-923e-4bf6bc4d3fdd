class ProcurementRequestsController < ApplicationController
  before_action :authenticate_session!
  before_action :set_procurement_request, only: %i[show update destroy]

  api :GET, "/procurement_requests", "Lists all procurement requests"
  header "Authorization", "Scoped session token as Bearer token", required: true
  description <<-HTML
    Returns a list of procurement requests.<br>
    Requires permission: <code>:read, :procurement_request</code>.
  HTML
  def index
    return unless authorize!(:read, :procurement_request)

    @procurement_requests = ProcurementRequest.all
    render json: @procurement_requests
  end

  api :GET, "/procurement_requests/:id", "Retrieves a specific request"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the request"
  description <<-HTML
    Fetches a specific procurement request.<br>
    Requires permission: <code>:read, :procurement_request</code>.
  HTML
  def show
    return unless authorize!(:read, :procurement_request)

    render json: @procurement_request
  end

  api :POST, "/procurement_requests", "Creates a new procurement request"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :procurement_request, Hash, required: true, desc: "Request details" do
    param :item_id, String, required: true, desc: "ID of the item being requested"
    param :requester_id, String, required: true, desc: "ID of the employee/user"
    param :quantity, Integer, required: true, desc: "Quantity requested"
    param :note, String, desc: "Optional employee note"
  end
  description <<-HTML
    Creates a new procurement request.<br>
    Requires permission: <code>:create, :procurement_request</code>.
  HTML
  def create
    return unless authorize!(:create, :procurement_request)

    @procurement_request = ProcurementRequest.new(procurement_request_params.merge(status: "pending", submitted_at: Time.current))

    if @procurement_request.save
      render json: @procurement_request, status: :created
    else
      render json: @procurement_request.errors, status: :unprocessable_entity
    end
  end

  api :PUT, "/procurement_requests/:id", "Updates a procurement request"
  api :PATCH, "/procurement_requests/:id", "Partially updates a procurement request"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the request"
  description <<-HTML
    Updates an existing request.<br>
    Requires permission: <code>:update, :procurement_request</code>.
  HTML
  def update
    return unless authorize!(:update, :procurement_request)

    if @procurement_request.update(procurement_request_params)
      render json: @procurement_request
    else
      render json: @procurement_request.errors, status: :unprocessable_entity
    end
  end

  api :DELETE, "/procurement_requests/:id", "Deletes a request"
  header "Authorization", "Scoped session token as Bearer token", required: true
  param :id, Integer, required: true, desc: "ID of the request"
  description <<-HTML
    Deletes a procurement request.<br>
    Requires permission: <code>:destroy, :procurement_request</code>.
  HTML
  def destroy
    return unless authorize!(:destroy, :procurement_request)

    @procurement_request.destroy!
  end

  private

  def set_procurement_request
    @procurement_request = ProcurementRequest.find(params[:id])
  end

  def procurement_request_params
    params.require(:procurement_request).permit(:item_id, :requester_id, :quantity, :note, :status, :submitted_at, :decided_at)
  end
end
